create table AIT_CONFIRM_MAPPING
(
    BATCH_NO                 VARCHAR2(32)         not null,
    SOURCE_PROFESSIONAL_NODE VARCHAR2(200)        not null,
    SOURCE_PROCESS_NODE      VARCHAR2(200),
    SOURCE_CONFIRM_TABLE     VARCHAR2(200)        not null,
    TARGET_PROFESSIONAL_NODE VARCHAR2(200)        not null,
    TARGET_PROCESS_NODE      VARCHAR2(200),
    TARGET_CONFIRM_TABLE     VARCHAR2(200)        not null,
    IMPORT_TIME              DATE default SYSDATE not null,
    IMPORT_USER              VARCHAR2(50)
)
/

create index IDX_ACM_BATCH_NO
    on AIT_CONFIRM_MAPPING (BATCH_NO)
/

create index IDX_ACM_SOURCE_TABLE
    on AIT_CONFIRM_MAPPING (SOURCE_CONFIRM_TABLE)
/

create index IDX_ACM_TARGET_TABLE
    on AIT_CONFIRM_MAPPING (TARGET_CONFIRM_TABLE)
/


create table AIT_CONFIRM_MAPPING_RELATION
(
    <PERSON>               NUMBER not null
        primary key,
    SOURCE_REPORT_ID NUMBER not null,
    TARGET_REPORT_ID NUMBER not null,
    SYNC_STATUS      VARCHAR2(20) default 'ACTIVE',
    CREATE_TIME      DATE         default SYSDATE,
    CREATE_USER      VARCHAR2(50),
    LAST_SYNC_TIME   DATE,
    LAST_SYNC_USER   VARCHAR2(50),
    STOP_TIME        DATE,
    STOP_USER        VARCHAR2(50),
    RECOVER_TIME     DATE,
    RECOVER_USER     VARCHAR2(50),
    PHASE_ID         NUMBER,
    constraint UK_AIT_MAPPING
        unique (SOURCE_REPORT_ID, TARGET_REPORT_ID)
)
/

create index IDX_ACMR_SOURCE_ID
    on AIT_CONFIRM_MAPPING_RELATION (SOURCE_REPORT_ID)
/

create index IDX_ACMR_TARGET_ID
    on AIT_CONFIRM_MAPPING_RELATION (TARGET_REPORT_ID)
/

create index IDX_ACMR_SYNC_STATUS
    on AIT_CONFIRM_MAPPING_RELATION (SYNC_STATUS)
/

create index IDX_ACMR_CREATE_TIME
    on AIT_CONFIRM_MAPPING_RELATION (CREATE_TIME)
/


create sequence SEQ_AIT_MAPPING_RELATION
    nocache
/
