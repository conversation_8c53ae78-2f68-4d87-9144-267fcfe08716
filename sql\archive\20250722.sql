-- BPM文件存储表
-- 用于存储从BPM系统下载的文件信息和本地存储路径
CREATE TABLE BMP_FILE_STORAGE (
    ID VARCHAR2(64) NOT NULL,              -- 主键ID
    BMP_FILE_ID VARCHAR2(64) NOT NULL,     -- <PERSON><PERSON>原始文件ID
    BO_ID VARCHAR2(64) NOT NULL,           -- 业务对象ID
    FIELD_NAME VARCHAR2(50) NOT NULL,      -- 字段名称(如XX_FJ)
    FILE_NAME VARCHAR2(255) NOT NULL,      -- 原始文件名
    FILE_SIZE NUMBER(12),                  -- 文件大小(字节)
    STORAGE_PATH VARCHAR2(500) NOT NULL,   -- 系统文件库相对路径
    CREATE_DATE DATE DEFAULT SYSDATE,     -- 创建时间
    CREATE_USER VARCHAR2(50),             -- 创建用户
    REMARK VARCHAR2(500),                  -- 备注
    CONSTRAINT PK_BMP_FILE_STORAGE PRIMARY KEY (ID)
);

-- 创建索引提高查询性能
CREATE INDEX IDX_BMP_FILE_STORAGE_BMP_ID ON BMP_FILE_STORAGE(BMP_FILE_ID);
CREATE INDEX IDX_BMP_FILE_STORAGE_BO_ID ON BMP_FILE_STORAGE(BO_ID);
CREATE INDEX IDX_BMP_FILE_STORAGE_FIELD ON BMP_FILE_STORAGE(FIELD_NAME);

-- 添加表注释
COMMENT ON TABLE BMP_FILE_STORAGE IS 'BPM文件存储表，用于记录从BPM系统下载并存储到本地文件库的文件信息';
COMMENT ON COLUMN BMP_FILE_STORAGE.ID IS '主键ID';
COMMENT ON COLUMN BMP_FILE_STORAGE.BMP_FILE_ID IS 'BMP系统原始文件ID，用于去重判断';
COMMENT ON COLUMN BMP_FILE_STORAGE.BO_ID IS '业务对象ID，关联具体的业务记录';
COMMENT ON COLUMN BMP_FILE_STORAGE.FIELD_NAME IS '字段名称，如XX_FJ等';
COMMENT ON COLUMN BMP_FILE_STORAGE.FILE_NAME IS '原始文件名';
COMMENT ON COLUMN BMP_FILE_STORAGE.FILE_SIZE IS '文件大小，单位字节';
COMMENT ON COLUMN BMP_FILE_STORAGE.STORAGE_PATH IS '系统文件库中的相对存储路径';
COMMENT ON COLUMN BMP_FILE_STORAGE.CREATE_DATE IS '记录创建时间';
COMMENT ON COLUMN BMP_FILE_STORAGE.CREATE_USER IS '创建用户';
COMMENT ON COLUMN BMP_FILE_STORAGE.REMARK IS '备注信息';


create or replace view PHASE_MODEL as
SELECT m.TREEID,
       n.NODENAME || '(' || m.CODE || ')' AS NODENAME,
       n.NODENAME AS MODEL_NAME,
       n.CODE AS MODEL_CODE,
       REPLACE(m.NODENAME, '阶段', '') AS PHASE_NAME,
       m.CODE AS PHASE_CODE,
       n.UNIQUECODE,
       m.TEST_CODE,
       m.DL_CODE,
       m.RG_CODE,
       m.ZC_CODE,
       m.CURRENT_AIT_NODE,
       m.IS_USE_SCREEN
FROM DATAPACKAGETREE m
         LEFT JOIN DATAPACKAGETREE n ON m.PARENTID = n.TREEID
WHERE m.NODETYPE = 'phase'
/

comment on table PHASE_MODEL is '阶段型号'
/

UPDATE DATAPACKAGETREE child
SET child.IS_USE_SCREEN = (SELECT parent.IS_USE_SCREEN
                           FROM DATAPACKAGETREE parent
                           WHERE parent.TREEID = child.PARENTID
                             AND parent.NODETYPE = 'product')
WHERE child.NODETYPE = 'phase'
  AND EXISTS (SELECT 1
              FROM DATAPACKAGETREE parent
              WHERE parent.TREEID = child.PARENTID
                AND parent.NODETYPE = 'product'
                AND parent.IS_USE_SCREEN IS NOT NULL);