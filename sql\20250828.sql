CREATE TABLE AITEGROUND_MODEL (
    AITGM_ID                VARCHAR2(50) NOT NULL,
    AITGM_MODELNUMBER       VARCHAR2(50),
    AITGM_MODELNAME         VARCHAR2(200),
    AITGM_MODELPHASENUMBER  VARCHAR2(50),
    AITGM_AITPHASEIID       VARCHAR2(50),
    AITGM_MODELOID          VARCHAR2(100),
    AITGM_AITPHASENAME      VARCHAR2(200)
);

-- 添加表注释
COMMENT ON TABLE AITEGROUND_MODEL IS '型号基础信息表';

-- 添加字段注释
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_ID IS 'ID';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELNUMBER IS '型号代号';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELNAME IS '型号名称';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELPHASENUMBER IS '型号阶段';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_AITPHASEIID IS '阶段代号';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELOID IS '型号唯一标识';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_AITPHASENAME IS '阶段名称';
/