CREATE TABLE AITEGROUND_MODEL (
    AITGM_ID                VARCHAR2(50) NOT NULL,
    AITGM_MODELNUMBER       VARCHAR2(50),
    AITGM_MODELNAME         VARCHAR2(200),
    AITGM_MODELPHASENUMBER  VARCHAR2(50),
    AITGM_AITPHASEIID       VARCHAR2(50),
    AITGM_MODELOID          VARCHAR2(100),
    AITGM_AITPHASENAME      VARCHAR2(200)
);

-- 添加表注释
COMMENT ON TABLE AITEGROUND_MODEL IS '型号基础信息表';

-- 添加字段注释
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_ID IS 'ID';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELNUMBER IS '型号代号';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELNAME IS '型号名称';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELPHASENUMBER IS '型号阶段';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_AITPHASEIID IS '阶段代号';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_MODELOID IS '型号唯一标识';
COMMENT ON COLUMN AITEGROUND_MODEL.AITGM_AITPHASENAME IS '阶段名称';
/

CREATE TABLE AITEGROUND_MODEL_SYNC_LOG
(
    AITGMSL_ID             VARCHAR2(50) NOT NULL,
    AITGM_ID               VARCHAR2(50) NOT NULL,
    AITGMSL_SYNC_COUNT     NUMBER(10),
    AITGMSL_SYNC_TIME      VARCHAR2(50),
    AITGMSL_SYNC_DATA      CLOB,
    AITGMSL_SYNC_RESPONSE  VARCHAR2(4000)
);

-- 添加主键约束
ALTER TABLE AITEGROUND_MODEL_SYNC_LOG 
ADD CONSTRAINT PK_AITEGROUND_MODEL_SYNC_LOG PRIMARY KEY (AITGMSL_ID);

-- 添加外键约束（关联到型号基础信息表）
ALTER TABLE AITEGROUND_MODEL_SYNC_LOG 
ADD CONSTRAINT FK_AITEGROUND_MODEL_SYNC_LOG 
FOREIGN KEY (AITGM_ID) REFERENCES AITEGROUND_MODEL(AITGM_ID);

-- 添加表注释
COMMENT ON TABLE AITEGROUND_MODEL_SYNC_LOG IS 'AIT型号同步记录表';

-- 添加字段注释
COMMENT ON COLUMN AITEGROUND_MODEL_SYNC_LOG.AITGMSL_ID IS '同步记录ID';
COMMENT ON COLUMN AITEGROUND_MODEL_SYNC_LOG.AITGM_ID IS '型号ID（关联AITEGROUND_MODEL表）';
COMMENT ON COLUMN AITEGROUND_MODEL_SYNC_LOG.AITGMSL_SYNC_COUNT IS '第几次同步';
COMMENT ON COLUMN AITEGROUND_MODEL_SYNC_LOG.AITGMSL_SYNC_TIME IS '同步时间';
COMMENT ON COLUMN AITEGROUND_MODEL_SYNC_LOG.AITGMSL_SYNC_DATA IS '同步数据（JSON格式）';
COMMENT ON COLUMN AITEGROUND_MODEL_SYNC_LOG.AITGMSL_SYNC_RESPONSE IS '同步接口返回内容（JSON格式）';
/