-- =====================================================
-- 确认表推送签署功能数据库脚本
-- 创建时间：2025-07-15
-- 创建人：wanghq
-- 说明：创建签署推送任务表及相关数据库对象
-- =====================================================


-- 创建签署推送任务表
CREATE TABLE SIGN_PUSH_TASK (
    ID NUMBER NOT NULL,
    PAGE_TYPE VARCHAR2(100),                     -- 页面类型（report/launch/confirm）
    PAGE_TABLE VARCHAR2(100),                    -- 页面表名称（QUALITY_REPORT/LAUNCH_CONFIRM/QUALITY_REPORT_C）
    TABLE_ID NUMBER NOT NULL,                    -- 确认表ID（关联对应表的ID）
    TABLE_NAME VARCHAR2(255),                    -- 表名称（节点名称）
    TABLE_NUM VARCHAR2(100),                     -- 表序号
    PARENT_TITLE VARCHAR2(500),                  -- 所有父节点的标题组合（使用|分割）
    USER_ID NUMBER,                              -- 用户ID（关联SYS_USER.USER_ID）
    USER_NAME VARCHAR2(100),                     -- 用户名（关联SYS_USER.USER_NAME）
    USER_FULLNAME VARCHAR2(100),                 -- 用户姓名
    USER_WORKNO VARCHAR2(50),                    -- 用户工号
    UNSIGNED_CELLS CLOB,                         -- 未签署单元格信息（JSON格式）
    LOCAL_STATUS VARCHAR2(20) DEFAULT 'PENDING', -- 本地任务状态：PENDING(未完成)/COMPLETED(已完成)/CANCELLED(已取消)
    BPM_STATUS VARCHAR2(20) DEFAULT 'PENDING',   -- BPM任务状态：PENDING(待推送)/PUSHED(已推送)/COMPLETED(已完成)/FAILED(未完成)/CANCELLED(已取消)
    BPM_TASK_ID VARCHAR2(100),                   -- BPM系统返回的任务ID
    LOCAL_COMPLETE_TIME VARCHAR2(50),            -- 本地完成时间
    BPM_PUSH_TIME VARCHAR2(50),                  -- BPM推送时间
    BPM_COMPLETE_TIME VARCHAR2(50),              -- BPM完成时间
    CREATOR VARCHAR2(100),                       -- 创建人
    CREATE_TIME VARCHAR2(50),                    -- 创建时间
    REMARK VARCHAR2(500),                        -- 备注
    CONSTRAINT PK_SIGN_PUSH_TASK PRIMARY KEY (ID)
);

-- 创建序列
CREATE SEQUENCE SIGN_PUSH_TASK_SEQ 
    START WITH 1 
    INCREMENT BY 1 
    NOCACHE 
    NOCYCLE;

-- 创建索引
CREATE INDEX IDX_SPT_TABLE_ID ON SIGN_PUSH_TASK(TABLE_ID);
CREATE INDEX IDX_SPT_USER_ID ON SIGN_PUSH_TASK(USER_ID);
CREATE INDEX IDX_SPT_BPM_TASK_ID ON SIGN_PUSH_TASK(BPM_TASK_ID);
CREATE INDEX IDX_SPT_LOCAL_STATUS ON SIGN_PUSH_TASK(LOCAL_STATUS);
CREATE INDEX IDX_SPT_BPM_STATUS ON SIGN_PUSH_TASK(BPM_STATUS);
CREATE INDEX IDX_SPT_PAGE_TYPE ON SIGN_PUSH_TASK(PAGE_TYPE);
CREATE INDEX IDX_SPT_CREATE_TIME ON SIGN_PUSH_TASK(CREATE_TIME);

-- 添加表注释
COMMENT ON TABLE SIGN_PUSH_TASK IS '签署推送任务表，用于管理确认表系统与BPM系统集成的签署推送任务';

-- 添加字段注释
COMMENT ON COLUMN SIGN_PUSH_TASK.ID IS '主键ID';
COMMENT ON COLUMN SIGN_PUSH_TASK.PAGE_TYPE IS '页面类型：report-AIT质量确认，launch-发射场确认，confirm-产品质量确认';
COMMENT ON COLUMN SIGN_PUSH_TASK.PAGE_TABLE IS '页面对应的数据库表名：QUALITY_REPORT/LAUNCH_CONFIRM/QUALITY_REPORT_C';
COMMENT ON COLUMN SIGN_PUSH_TASK.TABLE_ID IS '确认表ID，关联对应确认表的主键';
COMMENT ON COLUMN SIGN_PUSH_TASK.TABLE_NAME IS '表名称，即节点名称';
COMMENT ON COLUMN SIGN_PUSH_TASK.TABLE_NUM IS '表序号';
COMMENT ON COLUMN SIGN_PUSH_TASK.PARENT_TITLE IS '所有父节点的标题组合，使用|符号分割';
COMMENT ON COLUMN SIGN_PUSH_TASK.USER_ID IS '用户ID，关联SYS_USER表的USER_ID字段';
COMMENT ON COLUMN SIGN_PUSH_TASK.USER_FULLNAME IS '用户姓名，对应SYS_USER表的USER_FULLNAME字段';
COMMENT ON COLUMN SIGN_PUSH_TASK.USER_WORKNO IS '用户工号，对应SYS_USER表的USER_WORKNO字段';
COMMENT ON COLUMN SIGN_PUSH_TASK.UNSIGNED_CELLS IS '未签署单元格信息，JSON格式：[{"row":1,"col":2,"position":"第2行第3列"}]';
COMMENT ON COLUMN SIGN_PUSH_TASK.LOCAL_STATUS IS '本地任务状态：PENDING-未完成，COMPLETED-已完成，CANCELLED-已取消';
COMMENT ON COLUMN SIGN_PUSH_TASK.BPM_STATUS IS 'BPM任务状态：PENDING-待推送，PUSHED-已推送，COMPLETED-已完成，FAILED-未完成，CANCELLED-已取消';
COMMENT ON COLUMN SIGN_PUSH_TASK.BPM_TASK_ID IS 'BPM系统返回的任务ID';
COMMENT ON COLUMN SIGN_PUSH_TASK.LOCAL_COMPLETE_TIME IS '本地完成时间';
COMMENT ON COLUMN SIGN_PUSH_TASK.BPM_PUSH_TIME IS 'BPM推送时间';
COMMENT ON COLUMN SIGN_PUSH_TASK.BPM_COMPLETE_TIME IS 'BPM完成时间';
COMMENT ON COLUMN SIGN_PUSH_TASK.CREATOR IS '创建人用户名';
COMMENT ON COLUMN SIGN_PUSH_TASK.CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN SIGN_PUSH_TASK.REMARK IS '备注信息';


alter table XMLDATA_PRODUCTSUBMIT
    add OTHER_CERTIFICATE1 VARCHAR2(255)
/

comment on column XMLDATA_PRODUCTSUBMIT.OTHER_CERTIFICATE1 is '其他证明材料1'
/

alter table XMLDATA_PRODUCTSUBMIT
    add OTHER_CERTIFICATE2 VARCHAR2(255)
/

comment on column XMLDATA_PRODUCTSUBMIT.OTHER_CERTIFICATE2 is '其他证明材料2'
/

-- 更新V_XMLDATA_PRODUCTSUBMIT视图，支持other_certificate1和other_certificate2字段的ISSUBMIT判断
-- 修改日期：2025-07-21
-- 修改人：wanghq
-- 修改内容：在ISSUBMIT字段计算中增加对other_certificate1和other_certificate2字段的OR逻辑判断

CREATE OR REPLACE VIEW V_XMLDATA_PRODUCTSUBMIT AS
SELECT
    ID,
    RESULT_ID,
    BILLCODE,
    PRODUCTNAME,
    PRODUCTCODE,
    BATCHCODE,
    LOCATION,
    PACKAGINGSTATE,
    IDENTIFYPPHYSICALOBJECTS,
    LISTINKIND,
    CODECORRECTNESS,
    SURFACESTATE,
    DIMENSIONMARKING,
    FASTENINGSTATE,
    INSPECTIONOFSOCKET,
    REDUNDANCYINSPECTION,
    CABLEINSPECTION,
    OPTICALMIRRORSTATE,
    OTHER,
    CERTIFICATENUMBER,
    RESUMENUMBER,
    PRODUCTSUBSTANCE,
    CONTENTSIGNATURE,
    COMPLETEINFORMATION,
    STORAGEPERIOD,
    QUALIFIEDCONCLUSION,
    COMPLETESIGNATURE,
    PRODUCTRESUME,
    TODOITEMS,
    TEMPERATUREANDHUMIDITY,
    INFORMATIONNOTE,
    ADFROMSUBMIT,
    SUBMITSIGNATURE,
    ADFROMTOTALUNIT,
    TOTALUNITSIGNATURE,
    ADFROMRECEIVE,
    RECEIVESIGNATURE,
    DOWNLOADURL,
    FILENAME,
    FILEPATH,
    SOURCE_ID,
    PRODUCT_ID,
    ISCERTIFICATE,
    ISLUOHAN,
    LUOHANPHASE,
    SUBMITUNIT,
    OTHER_CERTIFICATE1,
    OTHER_CERTIFICATE2,
    CASE
        WHEN
            -- 原有判断条件：CERTIFICATENUMBER和RESUMENUMBER数值之和不为0
            (CASE WHEN REGEXP_LIKE(CERTIFICATENUMBER, '^\d+$') THEN TO_NUMBER(CERTIFICATENUMBER) ELSE 0 END)
            +
            (CASE WHEN REGEXP_LIKE(RESUMENUMBER, '^\d+$') THEN TO_NUMBER(RESUMENUMBER) ELSE 0 END)
            > 0
            OR
            -- 新增判断条件：other_certificate1或other_certificate2包含'合格证'文本
            (OTHER_CERTIFICATE1 LIKE '%合格证%' OR OTHER_CERTIFICATE2 LIKE '%合格证%')
        THEN '已提交'
        ELSE '未提交'
    END AS ISSUBMIT
FROM
    XMLDATA_PRODUCTSUBMIT
/
