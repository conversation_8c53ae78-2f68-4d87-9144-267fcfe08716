create sequence NODE_CHANGE_RECORD_SEQ
/

create table NODE_CHANGE_RECORD
(
    ID              NUMBER not null
        primary key,
    OLD_NODE_ID     NUMBER,
    NEW_NODE_ID     NUMBER,
    OLD_NODE_NAME   NVARCHAR2(1024),
    NEW_NODE_NAME   NVARCHAR2(1024),
    OLD_PARENT_NAME NVARCHAR2(1024),
    NEW_PARENT_NAME NVARCHAR2(1024),
    CHANGE_TYPE     VARCHAR2(50),
    CHANGE_TIME     VARCHAR2(255),
    OPERATOR        VARCHAR2(255),
    BATCH_ID        VARCHAR2(100),
    REMARK          NVARCHAR2(2000),
    STAGE_ID        NUMBER
)
/

create or replace view DATAPKG_TREE_VIEW as
select "ID",
       "NAME",
       "CODE",
       "REMARK",
       "REFTREEID",
       "CREATOR",
       "CREATETIME",
       "TREEID",
       "PARENT<PERSON>",
       "NO<PERSON><PERSON><PERSON>",
       "NO<PERSON>CO<PERSON>",
       "NODETYP<PERSON>",
       "NO<PERSON><PERSON><PERSON>",
       "NO<PERSON><PERSON><PERSON><PERSON>",
       "NODESORT",
       "REFPROD",
       "UNIQUECODE",
       "NODESTATUS",
       "PDM_OID",
       "COPY_TREEID",
       "IS_USE_SCREEN",
       "MODEL_NAME",
       "MODEL_ID",
       "MODEL_CODE",
       "MODEL_SORT",
       "PHASE_NAME",
       "PHASE_ID",
       "PHASE_SORT",
       "DIR_NAME",
       "DIR_ID",
       "DIR_SORT",
       "LEAF_NAME",
       "LEAF_ID",
       "LEAF_SORT"
from DATA_PACKAGE t1
         left join TREE_ALL_VIEW t2 on t1.REFTREEID = t2.TREEID
/