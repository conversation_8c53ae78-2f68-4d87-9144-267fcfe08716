create table PANORAMA_DEVICE
(
    DEVICE_ID   NUMBER,
    TASK_ID     NUMBER,
    DEVICE_NAME VARCHAR2(255) not null,
    DEVICE_CODE VARCHAR2(100),
    BATCH_NO    VARCHAR2(100),
    SEQUENCE_NO NUMBER,
    MODEL_ID    VARCHAR2(100),
    <PERSON>OD<PERSON>_NAME  VARCHAR2(255),
    CREATE_TIME DATE default SYSDATE,
    UPDATE_TIME DATE default SYSDATE
)
/

comment on table PANORAMA_DEVICE is '单机信息表'
/

comment on column PANORAMA_DEVICE.DEVICE_ID is '单机ID'
/

comment on column PANORAMA_DEVICE.TASK_ID is '关联任务ID'
/

comment on column PANORAMA_DEVICE.DEVICE_NAME is '单机名称'
/

comment on column PANORAMA_DEVICE.DEVICE_CODE is '单机代号'
/

comment on column PANORAMA_DEVICE.BATCH_NO is '批次号'
/

comment on column PANORAMA_DEVICE.SEQUENCE_NO is '序号'
/

comment on column PANORAMA_DEVICE.MODEL_ID is '型号ID'
/

comment on column PANORAMA_DEVICE.MODEL_NAME is '型号名称'
/

comment on column PANORAMA_DEVICE.CREATE_TIME is '创建时间'
/

create index IDX_DEVICE_TASK_ID
    on PANORAMA_DEVICE (TASK_ID)
/

create index IDX_DEVICE_MODEL_ID
    on PANORAMA_DEVICE (MODEL_ID)
/


create table PANORAMA_HOTSPOT
(
    HOTSPOT_ID     NUMBER,
    TASK_ID        NUMBER,
    HOTSPOT_XML_ID VARCHAR2(100),
    DEVICE_ID      NUMBER,
    PAN            VARCHAR2(50),
    TILT           VARCHAR2(50),
    SKINID         VARCHAR2(100),
    URL            VARCHAR2(1000),
    TARGET         VARCHAR2(500),
    IS_EDITED      NUMBER(1) default 0,
    CREATE_TIME    DATE      default SYSDATE,
    UPDATE_TIME    DATE      default SYSDATE,
    PANORAMA_ID    VARCHAR2(100),
    TITLE          VARCHAR2(500),
    DESCRIPTION    VARCHAR2(1000)
)
/

comment on table PANORAMA_HOTSPOT is '全景图热点信息表'
/

comment on column PANORAMA_HOTSPOT.HOTSPOT_ID is '热点ID'
/

comment on column PANORAMA_HOTSPOT.TASK_ID is '关联任务ID'
/

comment on column PANORAMA_HOTSPOT.HOTSPOT_XML_ID is 'XML文件中的热点ID'
/

comment on column PANORAMA_HOTSPOT.DEVICE_ID is '关联单机信息ID'
/

comment on column PANORAMA_HOTSPOT.PAN is '水平角度'
/

comment on column PANORAMA_HOTSPOT.TILT is '垂直角度'
/

comment on column PANORAMA_HOTSPOT.SKINID is '热点皮肤ID'
/

comment on column PANORAMA_HOTSPOT.URL is '热点链接地址'
/

comment on column PANORAMA_HOTSPOT.TARGET is '热点目标'
/

comment on column PANORAMA_HOTSPOT.IS_EDITED is '是否已编辑：0-未编辑，1-已编辑'
/

comment on column PANORAMA_HOTSPOT.CREATE_TIME is '创建时间'
/

comment on column PANORAMA_HOTSPOT.UPDATE_TIME is '更新时间'
/

comment on column PANORAMA_HOTSPOT.PANORAMA_ID is '所属全景节点ID，用于多节点支持功能'
/

create index IDX_HOTSPOT_TASK_ID
    on PANORAMA_HOTSPOT (TASK_ID)
/

create index IDX_HOTSPOT_EDITED
    on PANORAMA_HOTSPOT (IS_EDITED)
/



create table PANORAMA_TASK
(
    TASK_ID       NUMBER,
    TASK_NAME     VARCHAR2(255) not null,
    MODEL_ID      VARCHAR2(100),
    MODEL_NAME    VARCHAR2(255),
    DESCRIPTION   VARCHAR2(1000),
    ZIP_FILE_PATH VARCHAR2(500),
    EXTRACT_PATH  VARCHAR2(500),
    CREATE_USER   VARCHAR2(50) default 'adm',
    CREATE_TIME   DATE         default SYSDATE,
    UPDATE_TIME   DATE         default SYSDATE,
    STATUS        NUMBER(1)    default 0
)
/

comment on table PANORAMA_TASK is '全景图任务管理表'
/

comment on column PANORAMA_TASK.TASK_ID is '任务ID'
/

comment on column PANORAMA_TASK.TASK_NAME is '任务名称'
/

comment on column PANORAMA_TASK.MODEL_ID is '型号ID'
/

comment on column PANORAMA_TASK.MODEL_NAME is '型号名称'
/

comment on column PANORAMA_TASK.DESCRIPTION is '任务描述'
/

comment on column PANORAMA_TASK.ZIP_FILE_PATH is 'ZIP文件存储路径'
/

comment on column PANORAMA_TASK.EXTRACT_PATH is 'ZIP文件解压路径'
/

comment on column PANORAMA_TASK.CREATE_USER is '创建用户'
/

comment on column PANORAMA_TASK.CREATE_TIME is '创建时间'
/

comment on column PANORAMA_TASK.UPDATE_TIME is '更新时间'
/

comment on column PANORAMA_TASK.STATUS is '任务状态：0-创建中，1-已完成，2-已导出'
/

create index IDX_TASK_STATUS
    on PANORAMA_TASK (STATUS)
/

create index IDX_TASK_CREATE_TIME
    on PANORAMA_TASK (CREATE_TIME)
/

create index IDX_TASK_MODEL_ID
    on PANORAMA_TASK (MODEL_ID)
/


CREATE SEQUENCE SEQ_PANORAMA_TASK
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
/

CREATE SEQUENCE SEQ_PANORAMA_HOTSPOT
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
/

CREATE SEQUENCE SEQ_PANORAMA_DEVICE
    START WITH 1
    INCREMENT BY 1
    NOCACHE
    NOCYCLE
/