-- =====================================================
-- BPM系统业务表DDL创建脚本
-- 创建日期：2025年5月27日
-- 描述：基于MD文档表结构定义生成的Oracle 11g兼容DDL语句
-- =====================================================

-- =====================================================
-- 1. 技术状态更改单落实情况检查表
-- 业务对象名：BO_EU_JSZTGG_NEW_H
-- 数据库表名：BPM_TC_CHANGE_ORDER
-- =====================================================
CREATE TABLE BPM_TC_CHANGE_ORDER (
    BILLNO VARCHAR2(50),                    -- 单据编号
    ISEND VARCHAR2(128),                    -- 是否结束
    VBILLSTATUS VARCHAR2(128),              -- 单据状态
    ZDRQ VARCHAR2(20),                      -- 制单日期
    ZDR VARCHAR2(20),                       -- 制单人
    ZDRID VARCHAR2(50),                     -- 制单人ID
    XH VARCHAR2(50),                        -- 型号
    YZJD VARCHAR2(50),                      -- 研制阶段
    CPDH VARCHAR2(100),                     -- 产品代号
    PCH_SL VARCHAR2(100),                   -- 批次号及数量
    GGDH VARCHAR2(100),                     -- 更改单号
    ZLGZQRRY VARCHAR2(50),                  -- 质量跟踪确认人员
    ZLGZQRRYID VARCHAR2(50),                -- 质量跟踪确认人员ID
    GGDFFBBH VARCHAR2(500),                 -- 更改单发放包编号
    GGDFFBMC VARCHAR2(100),                 -- 更改单发放包名称
    PDMFILELINK VARCHAR2(500),              -- PDM文件链接（输入文件）
    LZDW VARCHAR2(50),                      -- 来自单位
    SJSMC VARCHAR2(50),                     -- 设计师
    SRWJ_GGQ VARCHAR2(100),                 -- 更改前编号
    SRWJ_GGQ_MC VARCHAR2(100),              -- 更改前名称
    SRWJ_GGQ_BB VARCHAR2(100),              -- 更改前版本
    SRWJ_GGH VARCHAR2(100),                 -- 更改后编号
    SRWJ_GGH_MC VARCHAR2(100),              -- 更改后名称
    SRWJ_GGH_BB VARCHAR2(100),              -- 更改后版本
    GGXM VARCHAR2(128),                     -- 更改项目
    QSR VARCHAR2(20),                       -- 签署人
    QSRQ VARCHAR2(20),                      -- 签署日期
    BINDID VARCHAR2(50),                    -- 绑定ID
    CREATE_TIME DATE DEFAULT SYSDATE,       -- 创建时间
    UPDATE_TIME DATE DEFAULT SYSDATE        -- 更新时间
);

-- 添加表注释
COMMENT ON TABLE BPM_TC_CHANGE_ORDER IS '技术状态更改单落实情况检查表，对应BO_EU_JSZTGG_NEW_H';

-- 添加字段注释
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.BILLNO IS '单据编号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.ISEND IS '是否结束';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.VBILLSTATUS IS '单据状态';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.ZDRQ IS '制单日期';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.ZDR IS '制单人';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.ZDRID IS '制单人ID';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.XH IS '型号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.YZJD IS '研制阶段';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.CPDH IS '产品代号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.PCH_SL IS '批次号及数量';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.GGDH IS '更改单号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.ZLGZQRRY IS '质量跟踪确认人员';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.ZLGZQRRYID IS '质量跟踪确认人员ID';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.GGDFFBBH IS '更改单发放包编号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.GGDFFBMC IS '更改单发放包名称';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.PDMFILELINK IS 'PDM文件链接（输入文件）';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.LZDW IS '来自单位';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SJSMC IS '设计师';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SRWJ_GGQ IS '更改前编号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SRWJ_GGQ_MC IS '更改前名称';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SRWJ_GGQ_BB IS '更改前版本';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SRWJ_GGH IS '更改后编号';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SRWJ_GGH_MC IS '更改后名称';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.SRWJ_GGH_BB IS '更改后版本';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.GGXM IS '更改项目';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.QSR IS '签署人';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.QSRQ IS '签署日期';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.BINDID IS '绑定ID';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BPM_TC_CHANGE_ORDER.UPDATE_TIME IS '更新时间';

-- =====================================================
-- 2. 分支表
-- 业务对象名：BO_EU_JSZTGG_NEW_B
-- 数据库表名：BPM_TC_CHANGE_BRANCH
-- =====================================================
CREATE TABLE BPM_TC_CHANGE_BRANCH (
    FZ VARCHAR2(50),                        -- 分支
    YWBM VARCHAR2(50),                      -- 业务部门
    YWBMID VARCHAR2(100),                   -- 业务部门ID
    ISEND VARCHAR2(128),                    -- 是否结束
    GYRY VARCHAR2(20),                      -- 工艺人员
    GYSID VARCHAR2(50),                     -- 工艺师ID
    JYSID VARCHAR2(600),                    -- 检验师ID
    JTQK VARCHAR2(128),                     -- 具体情况
    SCWJ_GGQ VARCHAR2(100),                 -- 更改前编号
    SCWJ_GGQ_MC VARCHAR2(100),              -- 更改前名称
    SCWJ_GGQ_BB VARCHAR2(100),              -- 更改前版本
    SCWJ_GGH VARCHAR2(100),                 -- 更改后编号
    SCWJ_GGH_MC VARCHAR2(100),              -- 更改后名称
    SCWJ_GGH_BB VARCHAR2(100),              -- 更改后版本
    QTSM1 VARCHAR2(1000),                   -- 其它说明
    GYS VARCHAR2(20),                       -- 工艺师
    RQ1 VARCHAR2(20),                       -- 日期
    ZFCP_ZX_GGSL VARCHAR2(128),             -- 主6产品_在线_更改数量
    ZFCP_ZX_GGJL VARCHAR2(128),             -- 主6产品_在线_更改结论
    ZFCP_KF_GGSL VARCHAR2(128),             -- 主6产品_库房_更改数量
    ZFCP_KF_GGJL VARCHAR2(128),             -- 主6产品_库房_更改结论
    ZFCP_YJ_GGSL VARCHAR2(128),             -- 主6产品_已交_更改数量
    ZFCP_YJ_GGJL VARCHAR2(128),             -- 主6产品_已交_更改结论
    YQJ_GGQ VARCHAR2(128),                  -- 元器件（更改前）
    YQJ_GGH VARCHAR2(128),                  -- 元器件（更改后）
    QTSM2 VARCHAR2(1000),                   -- 其它说明
    JYY VARCHAR2(128),                      -- 检验人员
    RQ2 VARCHAR2(20),                       -- 日期
    YYBMQR VARCHAR2(500),                   -- 业务部门确认
    QZ3 VARCHAR2(20),                       -- 签字
    RQ3 VARCHAR2(20),                       -- 日期
    ZLSQR VARCHAR2(500),                    -- 质量师确认
    QZ4 VARCHAR2(20),                       -- 签字
    RQ4 VARCHAR2(20),                       -- 日期
    YJRY_GY VARCHAR2(20),                   -- 工艺移交人员
    YJRYID_GY VARCHAR2(50),                 -- 工艺移交人员ID
    YJRY_JY VARCHAR2(20),                   -- 检验移交人员
    YJRYID_JY VARCHAR2(50),                 -- 检验移交人员ID
    BINDID VARCHAR2(50),                    -- 绑定ID
    CREATE_TIME DATE DEFAULT SYSDATE,       -- 创建时间
    UPDATE_TIME DATE DEFAULT SYSDATE        -- 更新时间
);

-- 添加表注释
COMMENT ON TABLE BPM_TC_CHANGE_BRANCH IS '技术状态更改单分支表，对应BO_EU_JSZTGG_NEW_B';

-- 添加字段注释
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.FZ IS '分支';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YWBM IS '业务部门';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YWBMID IS '业务部门ID';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ISEND IS '是否结束';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.GYRY IS '工艺人员';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.GYSID IS '工艺师ID';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.JYSID IS '检验师ID';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.JTQK IS '具体情况';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.SCWJ_GGQ IS '更改前编号';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.SCWJ_GGQ_MC IS '更改前名称';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.SCWJ_GGQ_BB IS '更改前版本';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.SCWJ_GGH IS '更改后编号';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.SCWJ_GGH_MC IS '更改后名称';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.SCWJ_GGH_BB IS '更改后版本';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.QTSM1 IS '其它说明';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.GYS IS '工艺师';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.RQ1 IS '日期';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZFCP_ZX_GGSL IS '主6产品_在线_更改数量';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZFCP_ZX_GGJL IS '主6产品_在线_更改结论';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZFCP_KF_GGSL IS '主6产品_库房_更改数量';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZFCP_KF_GGJL IS '主6产品_库房_更改结论';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZFCP_YJ_GGSL IS '主6产品_已交_更改数量';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZFCP_YJ_GGJL IS '主6产品_已交_更改结论';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YQJ_GGQ IS '元器件（更改前）';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YQJ_GGH IS '元器件（更改后）';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.QTSM2 IS '其它说明';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.JYY IS '检验人员';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.RQ2 IS '日期';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YYBMQR IS '业务部门确认';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.QZ3 IS '签字';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.RQ3 IS '日期';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.ZLSQR IS '质量师确认';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.QZ4 IS '签字';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.RQ4 IS '日期';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YJRY_GY IS '工艺移交人员';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YJRYID_GY IS '工艺移交人员ID';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YJRY_JY IS '检验移交人员';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.YJRYID_JY IS '检验移交人员ID';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.BINDID IS '绑定ID';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BPM_TC_CHANGE_BRANCH.UPDATE_TIME IS '更新时间';

-- =====================================================
-- 3. 不合格品审理单表
-- 业务对象名：BO_EU_BHGPSLD
-- 数据库表名：BPM_NONCONFORMITY_REVIEW
-- =====================================================
CREATE TABLE BPM_NONCONFORMITY_REVIEW (
    BILLNO VARCHAR2(128),                   -- 单据编号
    VBILLSTATUS VARCHAR2(128),              -- 单据状态
    ISEND VARCHAR2(128),                    -- 是否结束
    ZDRQ VARCHAR2(30),                      -- 制单日期
    BZRY VARCHAR2(50),                      -- 发起人
    BZRYID VARCHAR2(50),                    -- 填报人ID
    BZBM VARCHAR2(50),                      -- 发起部门
    BZBMID VARCHAR2(50),                    -- 发起部门ID
    XH VARCHAR2(20),                        -- 型号
    YZJD VARCHAR2(30),                      -- 研制阶段
    CP VARCHAR2(30),                        -- 产品（或零部组件）
    CPBH VARCHAR2(30),                      -- 产品编号
    PCH VARCHAR2(30),                       -- 批次号
    CPTH VARCHAR2(128),                     -- 产品图号（代号）
    GXH VARCHAR2(30),                       -- 工序号
    FXDD VARCHAR2(30),                      -- 发现地点
    BHGSL VARCHAR2(30),                     -- 不合格品数量
    ZRBM VARCHAR2(30),                      -- 责任部门
    CZZ VARCHAR2(30),                       -- 操作者
    SJS VARCHAR2(30),                       -- 送检数
    FXRQ VARCHAR2(30),                      -- 发现日期
    SLRQ VARCHAR2(30),                      -- 审理日期
    SLDD VARCHAR2(30),                      -- 审理地点
    BHGPQKMS VARCHAR2(300),                 -- 不合格品情况描述
    BHGPYZCD VARCHAR2(20),                  -- 不合格品严重程度
    YYFL VARCHAR2(128),                     -- 原因分类
    YYFX VARCHAR2(300),                     -- 原因分析
    BHGPSLYJ VARCHAR2(200),                 -- 不合格品审理意见
    JZ VARCHAR2(500),                       -- 纠正
    JZCS VARCHAR2(500),                     -- 纠正措施
    TJXHZTSC VARCHAR2(128),                 -- 提交型号总体审查
    TJYBHGPWWH VARCHAR2(128),               -- 提交院不合格品审查
    TJJDBSC VARCHAR2(128),                  -- 提交军代表审查
    QZ1 VARCHAR2(128),                      -- 签字1
    RQ1 VARCHAR2(128),                      -- 日期1
    QZ2 VARCHAR2(128),                      -- 签字2
    RQ2 VARCHAR2(128),                      -- 日期2
    QZ3 VARCHAR2(128),                      -- 签字3
    RQ3 VARCHAR2(128),                      -- 日期3
    QZ4 VARCHAR2(128),                      -- 签字4
    RQ4 VARCHAR2(128),                      -- 日期4
    YJ1 VARCHAR2(128),                      -- 意见
    YJ2 VARCHAR2(128),                      -- 意见
    YJ3 VARCHAR2(128),                      -- 意见
    YJ4 VARCHAR2(128),                      -- 意见
    QZ5 VARCHAR2(128),                      -- 签字5
    YJ5 VARCHAR2(128),                      -- 意见
    RQ5 VARCHAR2(128),                      -- 日期5
    BHGPCZLSQK VARCHAR2(128),               -- 不合格品处置落实情况
    JZCSLSQK VARCHAR2(128),                 -- 纠正措施落实情况
    QZ6 VARCHAR2(128),                      -- 签字6
    QZ7 VARCHAR2(128),                      -- 签字7
    RQ6 VARCHAR2(128),                      -- 日期6
    RQ7 VARCHAR2(128),                      -- 日期7
    SCSCYJ VARCHAR2(128),                   -- 上传审查意见
    BHGPSLZZ VARCHAR2(128),                 -- 不合格品审理组长
    BHGPSLZZID VARCHAR2(128),               -- 不合格品审理组长ID
    CREATE_TIME DATE DEFAULT SYSDATE,       -- 创建时间
    UPDATE_TIME DATE DEFAULT SYSDATE        -- 更新时间
);

-- 添加表注释
COMMENT ON TABLE BPM_NONCONFORMITY_REVIEW IS '不合格品审理单表，对应BO_EU_BHGPSLD';

-- 添加字段注释
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BILLNO IS '单据编号';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.VBILLSTATUS IS '单据状态';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.ISEND IS '是否结束';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.ZDRQ IS '制单日期';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BZRY IS '发起人';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BZRYID IS '填报人ID';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BZBM IS '发起部门';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BZBMID IS '发起部门ID';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.XH IS '型号';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YZJD IS '研制阶段';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.CP IS '产品（或零部组件）';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.CPBH IS '产品编号';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.PCH IS '批次号';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.CPTH IS '产品图号（代号）';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.GXH IS '工序号';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.FXDD IS '发现地点';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGSL IS '不合格品数量';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.ZRBM IS '责任部门';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.CZZ IS '操作者';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.SJS IS '送检数';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.FXRQ IS '发现日期';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.SLRQ IS '审理日期';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.SLDD IS '审理地点';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGPQKMS IS '不合格品情况描述';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGPYZCD IS '不合格品严重程度';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YYFL IS '原因分类';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YYFX IS '原因分析';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGPSLYJ IS '不合格品审理意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.JZ IS '纠正';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.JZCS IS '纠正措施';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.TJXHZTSC IS '提交型号总体审查';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.TJYBHGPWWH IS '提交院不合格品审查';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.TJJDBSC IS '提交军代表审查';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ1 IS '签字1';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ1 IS '日期1';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ2 IS '签字2';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ2 IS '日期2';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ3 IS '签字3';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ3 IS '日期3';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ4 IS '签字4';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ4 IS '日期4';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YJ1 IS '意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YJ2 IS '意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YJ3 IS '意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YJ4 IS '意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ5 IS '签字5';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.YJ5 IS '意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ5 IS '日期5';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGPCZLSQK IS '不合格品处置落实情况';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.JZCSLSQK IS '纠正措施落实情况';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ6 IS '签字6';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.QZ7 IS '签字7';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ6 IS '日期6';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.RQ7 IS '日期7';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.SCSCYJ IS '上传审查意见';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGPSLZZ IS '不合格品审理组长';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.BHGPSLZZID IS '不合格品审理组长ID';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN BPM_NONCONFORMITY_REVIEW.UPDATE_TIME IS '更新时间';

-- =====================================================
-- DDL脚本创建完成
-- 包含三个BPM业务表：
-- 1. BPM_TC_CHANGE_ORDER - 技术状态更改单落实情况检查表
-- 2. BPM_TC_CHANGE_BRANCH - 技术状态更改单分支表
-- 3. BPM_NONCONFORMITY_REVIEW - 不合格品审理单表
-- =====================================================
